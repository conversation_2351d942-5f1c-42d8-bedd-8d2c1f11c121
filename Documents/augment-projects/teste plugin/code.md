<?php
/**
 * Plugin TRP Delivery Express para WooCommerce
 * Método de envio baseado em peso com cálculo correto de impostos
 */

// Função para calcular o preço de envio TRP Delivery Express
function calcular_preco_trp_delivery($peso_total, $instance_id = 0) {
    $peso_total = floatval($peso_total);
    if ($peso_total <= 0) {
        $peso_total = 1; // Peso mínimo de 1kg
    }

    // Obter regras para esta instância específica
    $rules = get_option('trp_shipping_rules_' . $instance_id, array());
    $threshold_weight = get_option('trp_threshold_weight_' . $instance_id, 30);
    $extra_cost_per_kg = get_option('trp_extra_cost_per_kg_' . $instance_id, 0.35);

    // Se não houver regras específicas para esta instância, usar regras globais
    if (empty($rules)) {
        $rules = get_option('trp_shipping_rules_global', array());
        $threshold_weight = get_option('trp_threshold_weight_global', 30);
        $extra_cost_per_kg = get_option('trp_extra_cost_per_kg_global', 0.35);
    }

    // Se ainda não houver regras, usar tabela padrão
    if (empty($rules)) {
        $tabela_precos = array(
            2 => 4.26,
            5 => 5.29,
            10 => 6.40,
            15 => 8.45,
            20 => 10.44,
            25 => 12.59,
            30 => 15.01
        );

        $preco_base = 0;
        $peso_adicional = 0;

        if ($peso_total <= 2) {
            $preco_base = $tabela_precos[2];
        } elseif ($peso_total <= 5) {
            $preco_base = $tabela_precos[5];
        } elseif ($peso_total <= 10) {
            $preco_base = $tabela_precos[10];
        } elseif ($peso_total <= 15) {
            $preco_base = $tabela_precos[15];
        } elseif ($peso_total <= 20) {
            $preco_base = $tabela_precos[20];
        } elseif ($peso_total <= 25) {
            $preco_base = $tabela_precos[25];
        } elseif ($peso_total <= 30) {
            $preco_base = $tabela_precos[30];
        } else {
            $preco_base = $tabela_precos[30];
            $peso_adicional = $peso_total - 30;
        }

        $custo_adicional = $peso_adicional * 0.35;
        $preco_final = $preco_base + $custo_adicional;

        return round($preco_final, 2);
    }

    // Encontrar a regra aplicável
    $preco_base = 0;
    $regra_encontrada = false;

    foreach ($rules as $rule) {
        if ($peso_total >= $rule['min_weight'] && $peso_total <= $rule['max_weight']) {
            $preco_base = $rule['price'];
            $regra_encontrada = true;
            break;
        }
    }

    // Se não encontrou regra específica, verificar se está acima do limite
    if (!$regra_encontrada) {
        // Encontrar a regra com o maior peso máximo
        $max_rule = null;
        foreach ($rules as $rule) {
            if ($max_rule === null || $rule['max_weight'] > $max_rule['max_weight']) {
                $max_rule = $rule;
            }
        }

        if ($max_rule && $peso_total > $max_rule['max_weight']) {
            $preco_base = $max_rule['price'];
            $peso_adicional = $peso_total - $max_rule['max_weight'];
            $custo_adicional = $peso_adicional * $extra_cost_per_kg;
            return round($preco_base + $custo_adicional, 2);
        }

        // Se ainda não encontrou, usar o primeiro intervalo
        if (!empty($rules)) {
            $preco_base = $rules[0]['price'];
        }
    }

    return round($preco_base, 2);
}

// Registrar método de envio personalizado no WooCommerce
add_action('woocommerce_shipping_init', 'init_trp_delivery_method');

function init_trp_delivery_method() {
    if (!class_exists('WC_TRP_Delivery_Method')) {
        class WC_TRP_Delivery_Method extends WC_Shipping_Method {
            // Propriedade para status de imposto
            public $tax_status;

            public function __construct($instance_id = 0) {
                $this->id = 'trp_delivery';
                $this->instance_id = absint($instance_id);
                $this->method_title = __('TRP Delivery Express');
                $this->method_description = __('Método de envio TRP Delivery Express baseado em peso com regras dinâmicas e cálculo correto de impostos');
                $this->supports = array(
                    'shipping-zones',
                    'instance-settings',
                    'instance-settings-modal',
                );
                $this->enabled = "yes";
                $this->title = "TRP Delivery Express";
                $this->tax_status = 'taxable';

                $this->init();
            }

            function init() {
                $this->init_form_fields();
                $this->init_settings();
                $this->enabled = $this->get_option('enabled');
                $this->title = $this->get_option('title');
                $this->tax_status = $this->get_option('tax_status', 'taxable');

                add_action('woocommerce_update_options_shipping_' . $this->id, array($this, 'process_admin_options'));
            }

            function init_form_fields() {
                // Campos de configuração com opção de tributação
                $this->instance_form_fields = array(
                    'enabled' => array(
                        'title' => __('Ativar/Desativar'),
                        'type' => 'checkbox',
                        'description' => __('Ativar este método de envio'),
                        'default' => 'yes'
                    ),
                    'title' => array(
                        'title' => __('Título'),
                        'type' => 'text',
                        'description' => __('Título que aparece no checkout'),
                        'default' => __('TRP Delivery Express'),
                        'desc_tip' => true
                    ),
                    'tax_status' => array(
                        'title' => __('Status do Imposto'),
                        'type' => 'select',
                        'class' => 'wc-enhanced-select',
                        'default' => 'taxable',
                        'description' => __('Define se o frete deve ser tributado ou não'),
                        'desc_tip' => true,
                        'options' => array(
                            'taxable' => __('Tributável'),
                            'none' => __('Sem impostos')
                        )
                    ),
                    'config_button' => array(
                        'title' => __('Configurações Avançadas'),
                        'type' => 'title',
                        'description' => sprintf(
                            __('<a href="%s" class="button">Configurar Regras de Envio</a>'),
                            admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $this->instance_id)
                        ),
                    ),
                );
            }

            public function calculate_shipping($package = array()) {
                $weight = 0;
                $cost = 0;

                // Calcular peso total
                foreach ($package['contents'] as $item_id => $values) {
                    $_product = $values['data'];
                    $product_weight = $_product->get_weight();
                    $quantity = $values['quantity'];
                    if ($product_weight && is_numeric($product_weight) && $quantity && is_numeric($quantity)) {
                        $weight += floatval($product_weight) * intval($quantity);
                    }
                }

                if ($weight <= 0) {
                    $weight = 1;
                }

                $weight = wc_get_weight($weight, 'kg');
                $weight = floatval($weight);

                // Calcular preço com base nas regras dinâmicas
                $cost = calcular_preco_trp_delivery($weight, $this->instance_id);

                // Configuração correta do cálculo de impostos
                $taxes = array();
                if ($this->tax_status === 'taxable') {
                    // Obter a classe de imposto padrão para envio
                    $tax_class = get_option('woocommerce_shipping_tax_class');
                    if ($tax_class === 'inherit') {
                        $tax_class = '';
                    }

                    // Calcular impostos baseado na localização do cliente
                    $location = WC_Tax::get_shipping_tax_rates($tax_class, WC()->customer);
                    if (!empty($location)) {
                        $taxes = WC_Tax::calc_shipping_tax($cost, $location);
                    }
                }

                $rate = array(
                    'id' => $this->get_rate_id(),
                    'label' => $this->title . ' (' . number_format($weight, 2, ',', '.') . 'kg)',
                    'cost' => $cost,
                    'taxes' => $taxes,
                    'calc_tax' => $this->tax_status
                );

                $this->add_rate($rate);
            }

            /**
             * Verifica se o método está disponível
             */
            public function is_available($package) {
                return $this->enabled === 'yes';
            }
        }
    }
}

// Adicionar o método de envio ao WooCommerce
add_filter('woocommerce_shipping_methods', 'add_trp_delivery_method');

function add_trp_delivery_method($methods) {
    $methods['trp_delivery'] = 'WC_TRP_Delivery_Method';
    return $methods;
}

// Adicionar menu para gerenciar regras de envio
add_action('admin_menu', 'trp_delivery_admin_menu');

function trp_delivery_admin_menu() {
    add_submenu_page(
        'woocommerce',
        'Regras de Envio TRP',
        'Regras de Envio TRP',
        'manage_woocommerce',
        'trp-shipping-rules',
        'trp_shipping_rules_page'
    );
}

// Hook para garantir que os impostos sejam calculados corretamente
add_action('woocommerce_cart_calculate_fees', 'trp_ensure_shipping_tax_calculation');

function trp_ensure_shipping_tax_calculation() {
    // Força o recálculo dos impostos quando necessário
    if (WC()->cart && !WC()->cart->is_empty()) {
        WC()->cart->calculate_shipping();
        WC()->cart->calculate_totals();
    }
}

// Página de administração para gerenciar regras
function trp_shipping_rules_page() {
    // Verificar se estamos editando uma instância específica
    $instance_id = isset($_GET['instance_id']) ? intval($_GET['instance_id']) : 0;
    $instance_text = $instance_id > 0 ? 'para Zona #' . $instance_id : 'Globais';

    // Determinar quais opções usar
    $rules_option_name = 'trp_shipping_rules_' . ($instance_id > 0 ? $instance_id : 'global');
    $threshold_option_name = 'trp_threshold_weight_' . ($instance_id > 0 ? $instance_id : 'global');
    $extra_cost_option_name = 'trp_extra_cost_per_kg_' . ($instance_id > 0 ? $instance_id : 'global');

    // Processar formulário quando enviado
    if (isset($_POST['trp_add_rule']) && check_admin_referer('trp_add_shipping_rule')) {
        $min_weight = isset($_POST['min_weight']) ? floatval($_POST['min_weight']) : 0;
        $max_weight = isset($_POST['max_weight']) ? floatval($_POST['max_weight']) : 0;
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;

        // Validar dados
        if ($max_weight > $min_weight && $price >= 0) {
            // Obter regras existentes
            $rules = get_option($rules_option_name, array());

            // Adicionar nova regra
            $rules[] = array(
                'min_weight' => $min_weight,
                'max_weight' => $max_weight,
                'price' => $price
            );

            // Ordenar regras por peso mínimo
            usort($rules, function($a, $b) {
                return $a['min_weight'] <=> $b['min_weight'];
            });

            // Salvar regras e limpar cache de envio
            update_option($rules_option_name, $rules);
            WC_Cache_Helper::get_transient_version('shipping', true);
            echo '<div class="notice notice-success"><p>Regra adicionada com sucesso!</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Erro: Verifique se o peso máximo é maior que o mínimo e o preço é válido.</p></div>';
        }
    }
    
    // Processar edição de regra
    if (isset($_POST['trp_edit_rule']) && check_admin_referer('trp_edit_shipping_rule')) {
        $rule_index = isset($_POST['rule_index']) ? intval($_POST['rule_index']) : -1;
        $min_weight = isset($_POST['min_weight']) ? floatval($_POST['min_weight']) : 0;
        $max_weight = isset($_POST['max_weight']) ? floatval($_POST['max_weight']) : 0;
        $price = isset($_POST['price']) ? floatval($_POST['price']) : 0;

        // Validar dados
        if ($rule_index >= 0 && $max_weight > $min_weight && $price >= 0) {
            // Obter regras existentes
            $rules = get_option($rules_option_name, array());

            if (isset($rules[$rule_index])) {
                // Atualizar regra
                $rules[$rule_index] = array(
                    'min_weight' => $min_weight,
                    'max_weight' => $max_weight,
                    'price' => $price
                );

                // Ordenar regras por peso mínimo
                usort($rules, function($a, $b) {
                    return $a['min_weight'] <=> $b['min_weight'];
                });

                // Salvar regras e limpar cache de envio
                update_option($rules_option_name, $rules);
                WC_Cache_Helper::get_transient_version('shipping', true);
                echo '<div class="notice notice-success"><p>Regra atualizada com sucesso!</p></div>';
            }
        } else {
            echo '<div class="notice notice-error"><p>Erro: Verifique se o peso máximo é maior que o mínimo e o preço é válido.</p></div>';
        }
    }

    // Processar exclusão de regra
    if (isset($_GET['delete_rule']) && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'delete_shipping_rule')) {
        $rule_index = intval($_GET['delete_rule']);
        $rules = get_option($rules_option_name, array());

        if (isset($rules[$rule_index])) {
            array_splice($rules, $rule_index, 1);
            update_option($rules_option_name, $rules);
            WC_Cache_Helper::get_transient_version('shipping', true);
            echo '<div class="notice notice-success"><p>Regra removida com sucesso!</p></div>';
        }
    }

    // Processar configuração de custo adicional
    if (isset($_POST['trp_save_extra_cost']) && check_admin_referer('trp_save_extra_cost')) {
        $threshold_weight = isset($_POST['threshold_weight']) ? floatval($_POST['threshold_weight']) : 0;
        $extra_cost_per_kg = isset($_POST['extra_cost_per_kg']) ? floatval($_POST['extra_cost_per_kg']) : 0;

        update_option($threshold_option_name, $threshold_weight);
        update_option($extra_cost_option_name, $extra_cost_per_kg);
        WC_Cache_Helper::get_transient_version('shipping', true);
        echo '<div class="notice notice-success"><p>Configurações de custo adicional salvas!</p></div>';
    }
    
    // Verificar se estamos editando uma regra
    $editing_rule = false;
    $edit_rule_index = -1;
    $edit_rule = array(
        'min_weight' => '',
        'max_weight' => '',
        'price' => ''
    );
    
    if (isset($_GET['edit_rule']) && isset($_GET['_wpnonce']) && wp_verify_nonce($_GET['_wpnonce'], 'edit_shipping_rule')) {
        $edit_rule_index = intval($_GET['edit_rule']);
        $rules = get_option($rules_option_name, array());
        
        if (isset($rules[$edit_rule_index])) {
            $editing_rule = true;
            $edit_rule = $rules[$edit_rule_index];
        }
    }
    
    // Obter regras existentes
    $rules = get_option($rules_option_name, array());
    $threshold_weight = get_option($threshold_option_name, 30);
    $extra_cost_per_kg = get_option($extra_cost_option_name, 0.35);
    
    // Obter zonas de envio para navegação
    $zones = WC_Shipping_Zones::get_zones();
    
    // Exibir interface
    ?>
    <div class="wrap">
        <h1>Gerenciar Regras de Envio TRP <?php echo $instance_text; ?></h1>
        
        <div class="nav-tab-wrapper woo-nav-tab-wrapper">
            <a href="<?php echo admin_url('admin.php?page=trp-shipping-rules'); ?>" class="nav-tab <?php echo $instance_id === 0 ? 'nav-tab-active' : ''; ?>">
                Regras Globais
            </a>
            <?php foreach ($zones as $zone_id => $zone) : 
                $shipping_methods = $zone['shipping_methods'];
                foreach ($shipping_methods as $method) :
                    if ($method->id === 'trp_delivery') : ?>
                        <a href="<?php echo admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $method->instance_id); ?>" 
                           class="nav-tab <?php echo $instance_id === $method->instance_id ? 'nav-tab-active' : ''; ?>">
                            <?php echo esc_html($zone['zone_name']); ?>
                        </a>
                    <?php endif;
                endforeach;
            endforeach; ?>
        </div>
        
        <h2>Regras Atuais</h2>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th>Peso Mínimo (kg)</th>
                    <th>Peso Máximo (kg)</th>
                    <th>Preço (€)</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($rules)): ?>
                    <tr>
                        <td colspan="4">Nenhuma regra definida.</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($rules as $index => $rule): ?>
                        <tr>
                            <td><?php echo number_format($rule['min_weight'], 2, ',', '.'); ?></td>
                            <td><?php echo number_format($rule['max_weight'], 2, ',', '.'); ?></td>
                            <td><?php echo number_format($rule['price'], 2, ',', '.'); ?></td>
                            <td>
                                <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $instance_id . '&edit_rule=' . $index), 'edit_shipping_rule'); ?>" 
                                   class="button">
                                    Editar
                                </a>
                                <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $instance_id . '&delete_rule=' . $index), 'delete_shipping_rule'); ?>" 
                                   class="button" onclick="return confirm('Tem certeza que deseja excluir esta regra?');">
                                    Excluir
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        
        <?php if ($editing_rule): ?>
            <h2>Editar Regra</h2>
            <form method="post" action="">
                <?php wp_nonce_field('trp_edit_shipping_rule'); ?>
                <input type="hidden" name="rule_index" value="<?php echo $edit_rule_index; ?>">
                <table class="form-table">
                    <tr>
                        <th><label for="min_weight">Peso Mínimo (kg)</label></th>
                        <td><input type="number" step="0.01" min="0" name="min_weight" id="min_weight" class="regular-text" value="<?php echo esc_attr($edit_rule['min_weight']); ?>" required></td>
                    </tr>
                    <tr>
                        <th><label for="max_weight">Peso Máximo (kg)</label></th>
                        <td><input type="number" step="0.01" min="0" name="max_weight" id="max_weight" class="regular-text" value="<?php echo esc_attr($edit_rule['max_weight']); ?>" required></td>
                    </tr>
                    <tr>
                        <th><label for="price">Preço (€)</label></th>
                        <td><input type="number" step="0.01" min="0" name="price" id="price" class="regular-text" value="<?php echo esc_attr($edit_rule['price']); ?>" required></td>
                    </tr>
                </table>
                <p class="submit">
                    <input type="submit" name="trp_edit_rule" class="button-primary" value="Atualizar Regra">
                    <a href="<?php echo admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $instance_id); ?>" class="button">Cancelar</a>
                </p>
            </form>
        <?php else: ?>
            <h2>Adicionar Nova Regra</h2>
            <form method="post" action="">
                <?php wp_nonce_field('trp_add_shipping_rule'); ?>
                <table class="form-table">
                    <tr>
                        <th><label for="min_weight">Peso Mínimo (kg)</label></th>
                        <td><input type="number" step="0.01" min="0" name="min_weight" id="min_weight" class="regular-text" required></td>
                    </tr>
                    <tr>
                        <th><label for="max_weight">Peso Máximo (kg)</label></th>
                        <td><input type="number" step="0.01" min="0" name="max_weight" id="max_weight" class="regular-text" required></td>
                    </tr>
                    <tr>
                        <th><label for="price">Preço (€)</label></th>
                        <td><input type="number" step="0.01" min="0" name="price" id="price" class="regular-text" required></td>
                    </tr>
                </table>
                <p class="submit">
                    <input type="submit" name="trp_add_rule" class="button-primary" value="Adicionar Regra">
                </p>
            </form>
        <?php endif; ?>
        
        <h2>Configurar Custo Adicional</h2>
        <form method="post" action="">
            <?php wp_nonce_field('trp_save_extra_cost'); ?>
            <table class="form-table">
                <tr>
                    <th><label for="threshold_weight">Peso Limite (kg)</label></th>
                    <td>
                        <input type="number" step="0.01" min="0" name="threshold_weight" id="threshold_weight" 
                               value="<?php echo esc_attr($threshold_weight); ?>" class="regular-text" required>
                        <p class="description">Acima deste peso, será cobrado um valor adicional por kg</p>
                    </td>
                </tr>
                <tr>
                    <th><label for="extra_cost_per_kg">Custo por kg adicional (€)</label></th>
                    <td>
                        <input type="number" step="0.01" min="0" name="extra_cost_per_kg" id="extra_cost_per_kg" 
                               value="<?php echo esc_attr($extra_cost_per_kg); ?>" class="regular-text" required>
                    </td>
                </tr>
            </table>
            <p class="submit">
                <input type="submit" name="trp_save_extra_cost" class="button-primary" value="Salvar Configurações">
            </p>
        </form>
        
        <div class="notice notice-info">
            <p><strong>Nota:</strong> As regras são aplicadas com base no peso total do pedido. Se o peso estiver entre o mínimo e máximo de uma regra, o preço dessa regra será aplicado. Se o peso exceder o máximo da maior regra, será cobrado um valor adicional por kg excedente.</p>
        </div>
    </div>
    <?php
}

// Adicionar link para configuração na lista de métodos de envio
add_action('woocommerce_after_shipping_rate', 'add_trp_delivery_config_link', 10, 2);

function add_trp_delivery_config_link($method, $index) {
    if ($method->get_method_id() === 'trp_delivery' && is_admin()) {
        $instance_id = $method->get_instance_id();
        echo '<div class="trp-config-link" style="margin-top: 5px;">';
        echo '<a href="' . admin_url('admin.php?page=trp-shipping-rules&instance_id=' . $instance_id) . '" class="button button-small">Configurar Regras</a>';
        echo '</div>';
    }
}

// Adicionar mensagem de debug para verificar se as regras estão sendo salvas corretamente
add_action('admin_notices', 'trp_debug_shipping_rules');

function trp_debug_shipping_rules() {
    if (isset($_GET['page']) && $_GET['page'] === 'trp-shipping-rules' && isset($_GET['debug'])) {
        $instance_id = isset($_GET['instance_id']) ? intval($_GET['instance_id']) : 0;
        $rules_option_name = 'trp_shipping_rules_' . ($instance_id > 0 ? $instance_id : 'global');
        $rules = get_option($rules_option_name, array());
        
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>Debug:</strong> Regras salvas para instância ' . $instance_id . ':</p>';
        echo '<pre>' . print_r($rules, true) . '</pre>';
        echo '</div>';
    }
}

// Garantir que as opções sejam criadas quando o plugin for ativado
register_activation_hook(__FILE__, 'trp_delivery_activate');

function trp_delivery_activate() {
    // Criar opções globais se não existirem
    if (false === get_option('trp_shipping_rules_global')) {
        add_option('trp_shipping_rules_global', array());
    }

    if (false === get_option('trp_threshold_weight_global')) {
        add_option('trp_threshold_weight_global', 30);
    }

    if (false === get_option('trp_extra_cost_per_kg_global')) {
        add_option('trp_extra_cost_per_kg_global', 0.35);
    }

    // Limpar cache de envio para garantir que as novas configurações sejam aplicadas
    WC_Cache_Helper::get_transient_version('shipping', true);
}

// Hook para garantir que os impostos sejam recalculados quando necessário
add_action('woocommerce_checkout_update_order_review', 'trp_force_shipping_tax_recalculation');

function trp_force_shipping_tax_recalculation($post_data) {
    // Força o recálculo dos impostos de envio quando o checkout é atualizado
    if (WC()->cart && !WC()->cart->is_empty()) {
        WC()->cart->calculate_shipping();
        WC()->cart->calculate_totals();
    }
}

// Adicionar informações de debug para impostos (apenas para administradores)
add_action('wp_footer', 'trp_debug_tax_info');

function trp_debug_tax_info() {
    if (current_user_can('manage_options') && isset($_GET['trp_debug_tax'])) {
        $chosen_methods = WC()->session->get('chosen_shipping_methods');
        if (!empty($chosen_methods)) {
            foreach ($chosen_methods as $method) {
                if (strpos($method, 'trp_delivery') !== false) {
                    echo '<div style="position: fixed; bottom: 10px; right: 10px; background: white; border: 1px solid #ccc; padding: 10px; z-index: 9999;">';
                    echo '<strong>TRP Debug - Impostos:</strong><br>';
                    echo 'Método escolhido: ' . esc_html($method) . '<br>';
                    echo 'Status de impostos: ' . (WC()->cart->display_prices_including_tax() ? 'Incluindo' : 'Excluindo') . '<br>';
                    echo 'Total de impostos de envio: ' . wc_price(WC()->cart->get_shipping_tax()) . '<br>';
                    echo '</div>';
                }
            }
        }
    }
}

?>